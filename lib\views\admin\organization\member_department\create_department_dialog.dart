import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';

class CreateDepartmentDrawer extends StatefulWidget {
  const CreateDepartmentDrawer({super.key});

  @override
  State<CreateDepartmentDrawer> createState() => _CreateDepartmentDrawerState();
}

class _CreateDepartmentDrawerState extends State<CreateDepartmentDrawer> {
  bool btnLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _depNameController = TextEditingController();
  DepartmentModel departmentModel = DepartmentModel();

  /// 添加
  Future<void> createRequest(BuildContext context) async {
    if (!_formKey.currentState!.validate()) return;

    try {
      departmentModel.parentId = 'd0a9ece8-74ff-4ab6-8b4f-f674cdb79dfd';

      final params = departmentModel.toJson();
      params.remove('Id');
      await DepartmentApi.add(params);
      ToastManager.success('提交成功');
      context.pop();
    } finally {
      setState(() {
        btnLoading = false;
      });
    }
  }

  /// 打开添加部门弹窗
  void _showCreateDepartmentDrawer(BuildContext context) {
    bool isAddNext = false;

    AppDialog.show(
      width: 480,
      context: context,
      title: '添加部门',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppInput(
              label: "部门名称",
              labelPosition: LabelPosition.left,
              hintText: "部门名称",
              size: InputSize.medium,
              controller: _depNameController,
              maxLength: 30,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入部门名称';
                }
                return null;
              },
              onChanged: (value) {
                departmentModel.departmentName = value;
              },
            ),
            //TODO:部门添加功能表单
            SizedBox(height: 30, child: Text('上级部门')),
            SizedBox(height: 30, child: Text('部门负责人')),
            SizedBox(height: 30, child: Text('部门HRBP')),
            AppInput(
              label: "职能描述",
              labelPosition: LabelPosition.left,
              maxLines: 5,
              hintText: "职能描述",
              size: InputSize.medium,
              maxLength: 3000,
              onChanged: (value) {
                departmentModel.description = value;
              },
            ),
          ],
        ),
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          StatefulBuilder(
            builder: (context, setState) {
              return Checkbox(
                value: isAddNext,
                onChanged: (value) {
                  setState(() {
                    isAddNext = !isAddNext;
                  });
                },
              );
            },
          ),
          Text('继续新建下一条'),
          const SizedBox(width: 10),
          AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
          const SizedBox(width: 10),
          AppButton(text: '确定', type: ButtonType.primary, onPressed: () => createRequest(context)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _depNameController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: '添加部门',
      type: ButtonType.primary,
      loading: btnLoading,
      onPressed: () {
        _showCreateDepartmentDrawer(context);
      },
    );
  }
}
